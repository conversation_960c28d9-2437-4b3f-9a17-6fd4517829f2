# This file was generated using idf.py save-defconfig. It can be edited manually.
# Espressif IoT Development Framework (ESP-IDF) Project Minimal Configuration
#
CONFIG_IDF_TARGET="esp32s3"
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_LV_COLOR_16_SWAP=y
CONFIG_LV_MEM_CUSTOM=y
CONFIG_LV_MEMCPY_MEMSET_STD=y
#CONFIG_LV_TICK_CUSTOM=y
#CONFIG_LV_TICK_CUSTOM_INCLUDE="esp_timer.h"
CONFIG_LV_USE_PERF_MONITOR=y
CONFIG_LV_SPRINTF_CUSTOM=y
# CONFIG_LV_BUILD_EXAMPLES is not set

#
# USB
#
CONFIG_USB_HOST_CONTROL_TRANSFER_MAX_SIZE=1024
CONFIG_USB_HOST_HW_BUFFER_BIAS_IN=y

#Support long filenames
CONFIG_FATFS_LFN_HEAP=y

#
# SPIRAM for ESP32-S3-Touch-LCD-7
#
CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240=y
CONFIG_ESP32S3_SPIRAM_SUPPORT=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=4096
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=8192

#
# BSP Generic Configuration for ESP32-S3-Touch-LCD-7
#
CONFIG_BSP_DISPLAY_ENABLED=y
CONFIG_BSP_LCD_RGB_BUFFER_NUMS=2
CONFIG_BSP_LCD_RGB_REFRESH_MANUALLY=n
CONFIG_BSP_LCD_RGB_REFRESH_AUTO=y
CONFIG_BSP_DISPLAY_H_RES=800
CONFIG_BSP_DISPLAY_V_RES=480
CONFIG_BSP_LCD_PIXEL_CLOCK_HZ=16000000
CONFIG_BSP_LCD_HSYNC_GPIO_NUM=39
CONFIG_BSP_LCD_VSYNC_GPIO_NUM=40
CONFIG_BSP_LCD_DE_GPIO_NUM=41
CONFIG_BSP_LCD_PCLK_GPIO_NUM=42
CONFIG_BSP_LCD_DATA0_GPIO_NUM=15
CONFIG_BSP_LCD_DATA1_GPIO_NUM=7
CONFIG_BSP_LCD_DATA2_GPIO_NUM=6
CONFIG_BSP_LCD_DATA3_GPIO_NUM=5
CONFIG_BSP_LCD_DATA4_GPIO_NUM=4
CONFIG_BSP_LCD_DATA5_GPIO_NUM=9
CONFIG_BSP_LCD_DATA6_GPIO_NUM=46
CONFIG_BSP_LCD_DATA7_GPIO_NUM=3
CONFIG_BSP_LCD_DATA8_GPIO_NUM=8
CONFIG_BSP_LCD_DATA9_GPIO_NUM=16
CONFIG_BSP_LCD_DATA10_GPIO_NUM=1
CONFIG_BSP_LCD_DATA11_GPIO_NUM=14
CONFIG_BSP_LCD_DATA12_GPIO_NUM=21
CONFIG_BSP_LCD_DATA13_GPIO_NUM=47
CONFIG_BSP_LCD_DATA14_GPIO_NUM=48
CONFIG_BSP_LCD_DATA15_GPIO_NUM=45
CONFIG_BSP_LCD_DISP_GPIO_NUM=-1
CONFIG_BSP_LCD_BACKLIGHT_GPIO_NUM=2

#
# Touch Configuration for ESP32-S3-Touch-LCD-7
#
CONFIG_BSP_TOUCH_ENABLED=y
CONFIG_BSP_I2C_NUM=0
CONFIG_BSP_I2C_FAST_MODE=y
CONFIG_BSP_I2C_CLK_SPEED_HZ=400000
CONFIG_BSP_TOUCH_I2C_SCL_GPIO_NUM=20
CONFIG_BSP_TOUCH_I2C_SDA_GPIO_NUM=19
CONFIG_BSP_TOUCH_I2C_INT_GPIO_NUM=18