esp_toml_version = 1.0
firmware_images_url = "https://espzav.github.io/UVC-Camera-and-MSC-LVGL-Example/"

supported_apps = ["UVC_CAMERA_EXAMPLE_IDF_latest", "UVC_CAMERA_EXAMPLE_IDF_v5"]

[UVC_CAMERA_EXAMPLE_IDF_v5]
chipsets = ["ESP32-S3"]
image.esp32-s3 = "camera_example_esp32s3_release_v5_0.bin"
android_app_url = ""
ios_app_url = ""

[UVC_CAMERA_EXAMPLE_IDF_latest]
chipsets = ["ESP32-S3"]
image.esp32-s3 = "camera_example_esp32s3_latest.bin"
android_app_url = ""
ios_app_url = ""